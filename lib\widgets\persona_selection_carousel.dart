import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_card.dart';

/// A horizontal scrolling carousel for selecting personas
///
/// This widget provides:
/// - Horizontal scrolling with smooth animations
/// - PersonaCard widgets as carousel items
/// - Responsive design for different screen sizes
/// - Proper spacing between cards
/// - Selection state management
/// - Optional video playback on avatar tap
class PersonaSelectionCarousel extends StatefulWidget {
  final List<models.SystemPersona> personas;
  final String? selectedPersonaId;
  final List<String>? selectedPersonaIds;
  final Function(String?)? onPersonaSelected;
  final Function(List<String>)? onMultiPersonaSelected;
  final Function(models.SystemPersona)? onAvatarTap;
  final bool showSelectionIndicator;
  final bool allowMultiSelection;
  final int? maxSelections;
  final double? cardWidth;
  final double? cardHeight;
  final EdgeInsets? padding;

  const PersonaSelectionCarousel({
    super.key,
    required this.personas,
    this.selectedPersonaId,
    this.selectedPersonaIds,
    this.onPersonaSelected,
    this.onMultiPersonaSelected,
    this.onAvatarTap,
    this.showSelectionIndicator = true,
    this.allowMultiSelection = false,
    this.maxSelections,
    this.cardWidth,
    this.cardHeight,
    this.padding,
  });

  @override
  State<PersonaSelectionCarousel> createState() =>
      _PersonaSelectionCarouselState();
}

class _PersonaSelectionCarouselState extends State<PersonaSelectionCarousel> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _handlePersonaSelection(models.SystemPersona persona) {
    if (widget.allowMultiSelection) {
      // Handle multi-selection
      final currentSelections = List<String>.from(
        widget.selectedPersonaIds ?? [],
      );

      if (currentSelections.contains(persona.id)) {
        // Remove if already selected
        currentSelections.remove(persona.id);
      } else {
        // Add if not selected, but check max selections
        if (widget.maxSelections == null ||
            currentSelections.length < widget.maxSelections!) {
          currentSelections.add(persona.id!);
        }
      }

      widget.onMultiPersonaSelected?.call(currentSelections);
    } else {
      // Handle single selection
      widget.onPersonaSelected?.call(persona.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.personas.isEmpty) {
      return _buildEmptyState(context);
    }

    final cardWidth = widget.cardWidth ?? _getResponsiveCardWidth(context);
    final cardHeight = widget.cardHeight ?? _getResponsiveCardHeight(context);

    return Container(
      height:
          cardHeight + AppDimensions.spacingL + (AppDimensions.spacingM * 2),
      padding:
          widget.padding ??
          const EdgeInsets.symmetric(vertical: AppDimensions.spacingM),
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacingM),
        itemCount: widget.personas.length,
        itemBuilder: (context, index) {
          final persona = widget.personas[index];
          final isSelected = widget.allowMultiSelection
              ? widget.selectedPersonaIds?.contains(persona.id) ?? false
              : widget.selectedPersonaId == persona.id;
          final isLast = index == widget.personas.length - 1;

          return Container(
            margin: EdgeInsets.only(right: isLast ? 0 : AppDimensions.spacingM),
            child: PersonaCard(
              persona: persona,
              isSelected: isSelected,
              width: cardWidth,
              height: cardHeight,
              margin: EdgeInsets.zero,
              showSelectionIndicator: widget.showSelectionIndicator,
              onTap: () => _handlePersonaSelection(persona),
              onAvatarTap: widget.onAvatarTap != null
                  ? () => widget.onAvatarTap!(persona)
                  : null,
              // Pass all personas for swipe navigation
              allPersonas: widget.personas,
              // Handle persona changes from video modal
              onPersonaChanged: (changedPersona) {
                // Update selection to the persona that was last viewed
                updateSelectedPersona(changedPersona.id);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(AppDimensions.spacingL),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.profile,
              size: AppDimensions.iconXxl,
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'No coaches available',
              style: context.textTheme.bodyLarge?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  double _getResponsiveCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < AppDimensions.mobileBreakpoint) {
      // Mobile: Show 1.5 cards
      return screenWidth * 0.7;
    } else if (screenWidth < AppDimensions.tabletBreakpoint) {
      // Tablet: Show 2.5 cards
      return screenWidth * 0.4;
    } else {
      // Desktop: Fixed width
      return 280;
    }
  }

  double _getResponsiveCardHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < AppDimensions.mobileBreakpoint) {
      // Mobile: Taller cards to accommodate avatar (280px) + content sections
      return 500;
    } else if (screenWidth < AppDimensions.tabletBreakpoint) {
      // Tablet: Adequate height for all content sections
      return 500;
    } else {
      // Desktop: Standard height with proper content display
      return 500;
    }
  }

  /// Scroll to a specific persona by ID
  void scrollToPersona(String personaId) {
    final index = widget.personas.indexWhere((p) => p.id == personaId);
    if (index != -1) {
      final cardWidth = _getResponsiveCardWidth(context);
      final targetOffset = index * (cardWidth + AppDimensions.spacingM);

      _scrollController.animateTo(
        targetOffset,
        duration: const Duration(
          milliseconds: AppDimensions.animationDurationNormal,
        ),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Scroll to the beginning of the carousel
  void scrollToStart() {
    _scrollController.animateTo(
      0,
      duration: const Duration(
        milliseconds: AppDimensions.animationDurationNormal,
      ),
      curve: Curves.easeInOut,
    );
  }

  /// Scroll to the end of the carousel
  void scrollToEnd() {
    _scrollController.animateTo(
      _scrollController.position.maxScrollExtent,
      duration: const Duration(
        milliseconds: AppDimensions.animationDurationNormal,
      ),
      curve: Curves.easeInOut,
    );
  }

  /// Update the selected persona from external source (e.g., video modal)
  /// This method allows parent components to synchronize selection state
  void updateSelectedPersona(String? personaId) {
    if (widget.allowMultiSelection) {
      // For multi-selection, add to selection if not already selected
      final currentSelections = List<String>.from(
        widget.selectedPersonaIds ?? [],
      );

      if (personaId != null && !currentSelections.contains(personaId)) {
        if (widget.maxSelections == null ||
            currentSelections.length < widget.maxSelections!) {
          currentSelections.add(personaId);
          widget.onMultiPersonaSelected?.call(currentSelections);
        }
      }
    } else {
      // For single selection, update the selected persona
      widget.onPersonaSelected?.call(personaId);
    }

    // Scroll to the selected persona if it exists
    if (personaId != null) {
      scrollToPersona(personaId);
    }
  }
}
