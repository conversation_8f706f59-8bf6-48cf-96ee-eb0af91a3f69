import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';

/// Step 1: Name Input
/// User enters their first name with live preview feedback
class NameInputStep extends StatefulWidget {
  final Function(String) onNameChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final String initialName;

  const NameInputStep({
    super.key,
    required this.onNameChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialName = '',
  });

  @override
  State<NameInputStep> createState() => _NameInputStepState();
}

class _NameInputStepState extends State<NameInputStep> {
  late TextEditingController _nameController;
  String _currentName = '';

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialName);
    _currentName = widget.initialName;
    _nameController.addListener(_onNameChanged);
  }

  @override
  void dispose() {
    _nameController.removeListener(_onNameChanged);
    _nameController.dispose();
    super.dispose();
  }

  void _onNameChanged() {
    final newName = _nameController.text.trim();
    if (newName != _currentName) {
      setState(() {
        _currentName = newName;
      });
      widget.onNameChanged(newName);
    }
  }

  bool get _canContinue => _currentName.isNotEmpty && _currentName.length <= 50;

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: 1,
      totalSteps: 5,
      title: 'How should we call you?',
      subtitle: 'Your coach will use this name during conversations.',
      onBack: widget.onBack,
      onSkip: widget.onSkip,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Name input field
            TextField(
              controller: _nameController,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: AppColors.textPrimary,
              ),
              decoration: InputDecoration(
                hintText: 'How should we call you?',
                hintStyle: const TextStyle(color: AppColors.textSecondary),
                filled: true,
                fillColor: AppColors.bg2,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: const BorderSide(color: AppColors.line, width: 1),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: const BorderSide(color: AppColors.line, width: 1),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: const BorderSide(
                    color: AppColors.accent,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.all(20),
              ),
              textCapitalization: TextCapitalization.words,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.done,
              maxLength: 50,
              buildCounter:
                  (
                    context, {
                    required currentLength,
                    required isFocused,
                    maxLength,
                  }) {
                    return null; // Hide the counter
                  },
            ),

            // Validation message
            if (_currentName.length > 50)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Name must be 50 characters or less',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.error,
                  ),
                ),
              ),

            // Add some bottom spacing to ensure content is not cut off
            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }
}
