import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_player.dart';

/// A modal dialog for displaying persona introduction videos with swipe navigation
///
/// This widget provides:
/// - Full-screen modal presentation
/// - Video player with hidden controls
/// - Horizontal swipe navigation between personas
/// - Wraparound navigation (first/last persona)
/// - Selection synchronization callback
/// - Proper modal lifecycle management
/// - Responsive design
class PersonaVideoModal extends StatefulWidget {
  final List<models.SystemPersona> personas;
  final int initialIndex;
  final Function(models.SystemPersona)? onPersonaChanged;

  const PersonaVideoModal({
    super.key,
    required this.personas,
    required this.initialIndex,
    this.onPersonaChanged,
  });

  /// Show the video modal in fullscreen with swipe navigation
  static Future<void> show(
    BuildContext context, {
    required List<models.SystemPersona> personas,
    required int initialIndex,
    Function(models.SystemPersona)? onPersonaChanged,
  }) {
    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black,
      barrierLabel: 'Close video modal',
      pageBuilder: (context, animation, secondaryAnimation) =>
          PersonaVideoModal(
            personas: personas,
            initialIndex: initialIndex,
            onPersonaChanged: onPersonaChanged,
          ),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  /// Legacy method for backward compatibility - shows single persona
  static Future<void> showSingle(
    BuildContext context, {
    required models.SystemPersona persona,
  }) {
    return show(context, personas: [persona], initialIndex: 0);
  }

  @override
  State<PersonaVideoModal> createState() => _PersonaVideoModalState();
}

class _PersonaVideoModalState extends State<PersonaVideoModal> {
  late PageController _pageController;
  late int _currentIndex;
  static const int _infiniteScrollOffset =
      10000; // Large offset for infinite scroll

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    // Start at a high index to allow backward scrolling
    final initialPage = _infiniteScrollOffset + _currentIndex;
    _pageController = PageController(initialPage: initialPage);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _navigateToPersona(int direction) {
    // direction: -1 for previous, 1 for next
    final newIndex = (_currentIndex + direction) % widget.personas.length;
    final actualIndex = newIndex < 0 ? widget.personas.length - 1 : newIndex;

    setState(() {
      _currentIndex = actualIndex;
    });

    // Calculate the target page for infinite scroll
    final currentPage = _pageController.page?.round() ?? _infiniteScrollOffset;
    final targetPage = currentPage + direction;

    _pageController.animateToPage(
      targetPage,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    // Notify parent about persona change
    widget.onPersonaChanged?.call(widget.personas[actualIndex]);
  }

  void _onPageChanged(int pageIndex) {
    // Calculate the actual persona index from the infinite scroll page index
    final actualIndex =
        (pageIndex - _infiniteScrollOffset) % widget.personas.length;
    final normalizedIndex = actualIndex < 0
        ? widget.personas.length + actualIndex
        : actualIndex;

    setState(() {
      _currentIndex = normalizedIndex;
    });

    // Notify parent about persona change
    widget.onPersonaChanged?.call(widget.personas[normalizedIndex]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Fullscreen video player with swipe navigation
            Center(
              child: AspectRatio(
                aspectRatio: 9 / 16, // 9:16 aspect ratio for 1080x1920 videos
                child: _buildVideoPageView(context),
              ),
            ),

            // Close button overlay
            Positioned(
              top: AppDimensions.spacingM,
              right: AppDimensions.spacingM,
              child: _buildCloseButton(context),
            ),

            // Navigation indicators (optional - shows current persona)
            if (widget.personas.length > 1)
              Positioned(
                bottom: AppDimensions.spacingL,
                left: 0,
                right: 0,
                child: _buildPersonaIndicator(context),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPageView(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: _onPageChanged,
      itemBuilder: (context, pageIndex) {
        // Calculate the actual persona index for infinite scrolling
        final actualIndex =
            (pageIndex - _infiniteScrollOffset) % widget.personas.length;
        final normalizedIndex = actualIndex < 0
            ? widget.personas.length + actualIndex
            : actualIndex;
        final persona = widget.personas[normalizedIndex];

        return PersonaVideoPlayer(
          key: ValueKey('${persona.id}_$pageIndex'), // Unique key for each page
          videoPath: persona.videoUrl,
          autoPlay: true,
          looping: false,
          showControls: false, // Hide controls as per requirements
          aspectRatio: 9 / 16, // 9:16 aspect ratio for vertical videos
          onVideoEnded: () => _onVideoEnded(normalizedIndex),
          onNavigatePrevious: () => _navigateToPersona(-1),
          onNavigateNext: () => _navigateToPersona(1),
          onReplay: () => _replayCurrentVideo(),
          allPersonas: widget.personas,
          currentPersonaIndex: normalizedIndex,
        );
      },
    );
  }

  void _onVideoEnded(int personaIndex) {
    // Video ended - the PersonaVideoPlayer will show navigation controls
  }

  void _replayCurrentVideo() {
    // Force rebuild of current video player to restart video
    setState(() {});
  }

  Widget _buildPersonaIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingM,
        vertical: AppDimensions.spacingS,
      ),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacingM,
            vertical: AppDimensions.spacingS,
          ),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: AppDimensions.borderRadiusM,
          ),
          child: Text(
            widget.personas[_currentIndex].name,
            style: context.textTheme.bodyMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(Icons.close, color: Colors.black87, size: 24),
        tooltip: 'Close video',
      ),
    );
  }
}
